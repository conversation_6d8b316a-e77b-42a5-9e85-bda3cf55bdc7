import AI from './ai.js'
import { promises as fs } from 'fs';
import { processOrdersWithAPI } from './google-sheets.js';

// Очередь для последовательной обработки запросов к Google Sheets API
class GoogleSheetsQueue {
  constructor() {
    this.queue = [];
    this.isProcessing = false;
  }

  async add(requestData) {
    return new Promise((resolve, reject) => {
      this.queue.push({
        data: requestData,
        resolve,
        reject,
        timestamp: Date.now()
      });

      console.log(`[SheetsQueue] Добавлен запрос в очередь. Размер очереди: ${this.queue.length}`);

      if (!this.isProcessing) {
        this.processQueue();
      }
    });
  }

  async processQueue() {
    if (this.isProcessing || this.queue.length === 0) {
      return;
    }

    this.isProcessing = true;
    console.log(`[SheetsQueue] Начинаем обработку очереди. Запросов в очереди: ${this.queue.length}`);

    while (this.queue.length > 0) {
      const { data, resolve, reject, timestamp } = this.queue.shift();
      const waitTime = Date.now() - timestamp;

      try {
        console.log(`[SheetsQueue] Обрабатываем запрос (ждал в очереди ${waitTime}ms). Осталось в очереди: ${this.queue.length}`);
        const result = await processOrdersWithAPI(data);
        console.log(`[SheetsQueue] Запрос успешно обработан через Google Sheets API`);
        resolve(result);
      } catch (error) {
        console.error(`[SheetsQueue] Ошибка при обработке запроса:`, error);
        reject(error);
      }
    }

    console.log(`[SheetsQueue] Очередь обработана полностью`);
    this.isProcessing = false;
  }
}

// Создаем единственный экземпляр очереди
const googleSheetsQueue = new GoogleSheetsQueue();

// Функция для получения расписания из CDN кэшированного endpoint
async function getCachedScheduleData() {
  try {
    console.log('[SendToSheet] Получаем данные из CDN кэшированного endpoint...');

    const response = await fetch('https://tgapi-pink.vercel.app/api/cached-schedule', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();

    if (result.success && result.data) {
      console.log(`[SendToSheet] Данные получены из CDN кэша. Записей: ${result.data.total_records}`);
      return result.data;
    } else {
      throw new Error('Invalid cached schedule response');
    }
  } catch (error) {
    console.error('[SendToSheet] Ошибка получения CDN кэшированного расписания:', error);
    return null;
  }
}

// Google Sheets API используется напрямую, очередь не нужна

// Функции кэша теперь импортируются из schedule-cache.js

// Функция для парсинга времени из строки HH:MM в минуты
function parseTimeToMinutes(timeString) {
  if (!timeString || typeof timeString !== 'string') return null;

  const parts = timeString.split(':');
  if (parts.length !== 2) return null;

  const hours = parseInt(parts[0], 10);
  const minutes = parseInt(parts[1], 10);

  if (isNaN(hours) || isNaN(minutes)) return null;
  if (hours < 0 || hours > 23 || minutes < 0 || minutes > 59) return null;

  return hours * 60 + minutes;
}

// Функция для конвертации минут обратно в строку HH:MM
function minutesToTimeString(totalMinutes) {
  const hours = Math.floor(totalMinutes / 60);
  const minutes = totalMinutes % 60;
  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
}

// Функция для поиска ближайшего времени в расписании
function findClosestTime(targetTime, availableTimes, orderDate) {
  if (!targetTime || !availableTimes || availableTimes.length === 0) {
    return targetTime; // возвращаем исходное время если нет данных
  }

  const targetMinutes = parseTimeToMinutes(targetTime);
  if (targetMinutes === null) return targetTime;

  // Фильтруем времена для конкретной даты
  const itemsForDate = availableTimes.filter(item => item.date === orderDate && item.time);

  // Детальное логирование найденных записей
  console.log(`[TimeAdjust] Найдено ${itemsForDate.length} записей для даты ${orderDate}:`);
  itemsForDate.forEach((item, index) => {
    console.log(`[TimeAdjust]   ${index + 1}. День: ${item.day}, Дата: ${item.date}, Время: ${item.time}, Событие: ${item.event}`);
  });

  const timesForDate = itemsForDate
    .map(item => parseTimeToMinutes(item.time))
    .filter(minutes => minutes !== null)
    .sort((a, b) => a - b); // сортируем по возрастанию

  // Отладочное логирование доступных времен
  const availableTimesStr = timesForDate.map(minutes => minutesToTimeString(minutes)).join(', ');
  console.log(`[TimeAdjust] Доступные времена для даты ${orderDate}: [${availableTimesStr}]`);

  if (timesForDate.length === 0) {
    return targetTime; // если нет времен для этой даты, возвращаем исходное
  }

  // Если точное время есть в расписании
  if (timesForDate.includes(targetMinutes)) {
    return targetTime;
  }

  // Ищем ближайшее время ДО целевого времени
  let closestTime = null;
  let reason = '';

  for (let i = timesForDate.length - 1; i >= 0; i--) {
    if (timesForDate[i] <= targetMinutes) {
      closestTime = timesForDate[i];
      reason = 'ближайшее время до целевого';
      break;
    }
  }

  // Если не нашли время до целевого, берем самое раннее
  if (closestTime === null) {
    closestTime = timesForDate[0];
    reason = 'самое раннее время (целевое время раньше всех доступных)';
  }

  const resultTime = minutesToTimeString(closestTime);
  console.log(`[TimeAdjust] Выбрано время ${resultTime} (${reason}) для целевого времени ${targetTime}`);

  return resultTime;
}

// Функция для корректировки времени заказа на основе расписания
async function adjustOrderTime(order, scheduleData) {
  // Если данные расписания не переданы, возвращаем заказ без изменений
  if (!scheduleData || !scheduleData.data) {
    console.log('[TimeAdjust] Данные расписания отсутствуют, время не корректируется');
    return order;
  }

  const scheduleRecords = scheduleData.data;
  const orderDate = order.date;
  const orderTime = order.time;

  // Если нет даты или времени в заказе, ничего не делаем
  if (!orderDate || !orderTime) {
    console.log('[TimeAdjust] Нет даты или времени в заказе, корректировка не нужна');
    return order;
  }

  // Проверяем, есть ли такая дата в расписании
  const datesInSchedule = scheduleRecords.map(item => item.date).filter(Boolean);
  const uniqueDates = [...new Set(datesInSchedule)];

  if (!uniqueDates.includes(orderDate)) {
    console.log(`[TimeAdjust] Дата ${orderDate} не найдена в расписании, время не корректируется`);
    return order;
  }

  console.log(`[TimeAdjust] Дата ${orderDate} найдена в расписании, проверяем время ${orderTime}`);
  console.log(`[TimeAdjust] Всего записей в расписании: ${scheduleRecords.length}`);

  // Ищем ближайшее время
  const adjustedTime = findClosestTime(orderTime, scheduleRecords, orderDate);

  if (adjustedTime !== orderTime) {
    console.log(`[TimeAdjust] Время скорректировано: ${orderTime} → ${adjustedTime} для даты ${orderDate}`);
    return { ...order, time: adjustedTime };
  } else {
    console.log(`[TimeAdjust] Время ${orderTime} найдено в расписании, корректировка не нужна`);
    return order;
  }
}

let slugs = [];
try {
  const slugsRaw = await fs.readFile(new URL('./slugs.json', import.meta.url));
  slugs = JSON.parse(slugsRaw).slugs;
} catch (e) {
  console.error('Ошибка загрузки slugs.json:', e);
}

const chatToShop = {
  'Epiccarry Raids | MeldTeam': { shop: 'EpicCarry', currency: 'usd' },
  'Boosthive Raids | MeldTeam': { shop: 'Boosthive.eu', currency: 'eur' },
  '🚀(BIG) MiracleBoost|MeldTeam#0615 R+': { shop: 'OverGear', currency: 'usd' },
  'Skycoach Raids | MeldTeam': { shop: 'Skycoach', currency: 'usd' },
  'Koroboost Raids EU | MeldTeam': { shop: 'Koroboost', currency: 'eur' },
  'Mythic Store Raids | MeldTeam': { shop: 'Mythic Store', currency: 'eur' },
  'Buy-Boost Raids | Granas': { shop: 'Buy-Boost', currency: 'eur' },
  'ConquestCapped Raids | MeldTeam': { shop: 'ConquestCapped', currency: 'usd' },
  'WoWCarry Raids | MeldTeam': { shop: 'WoWCarry', currency: 'usd' },
  'Leprestore Raids | MeldTeam': { shop: 'Leprestore', currency: 'usd' },
  'Expcarry Raids | MeldTeam': { shop: 'Expcarry', currency: 'usd' },
  'ReinwinBoost Raids | MeldTeam': { shop: 'ReinwinBoost', currency: 'rub' },
  'BoostCraft Raids | MeldTeam': { shop: 'BoostCraft', currency: 'usd' },
  'PandaBoostMe | Volodkameld | MeldTeam': { shop: 'PandaBoostMe', currency: 'usd' },
  'Dving Raids | MeldTeam': { shop: 'Dving', currency: 'rub' },
  '🇪🇺 EU Raids ArmadaBoost | MeldTeam': { shop: 'ArmadaBoost', currency: 'usd' },
  'EU PlaynFun Raids | MeldTeam': { shop: 'Meta', currency: 'usd' },
  'reactionorder': { shop: 'EpicCarry', currency: 'usd' },
  '1356941142234759302': { shop: 'Skycoach', currency: 'usd' }
  // таблица названий конф магазов с валютами
};

// Левенштейн
function levenshtein(a, b) {
  const matrix = Array.from({ length: a.length + 1 }, () => []);
  for (let i = 0; i <= a.length; i++) matrix[i][0] = i;
  for (let j = 0; j <= b.length; j++) matrix[0][j] = j;
  for (let i = 1; i <= a.length; i++) {
    for (let j = 1; j <= b.length; j++) {
      matrix[i][j] = Math.min(
        matrix[i - 1][j] + 1,
        matrix[i][j - 1] + 1,
        matrix[i - 1][j - 1] + (a[i - 1] === b[j - 1] ? 0 : 1)
      );
    }
  }
  return matrix[a.length][b.length];
}

// Функция для проверки, стоит ли подстраивать сервер или лучше очистить
function shouldAdjustServer(originalServer, suggestedServer) {
  const normalizedOriginal = originalServer.toLowerCase();
  const normalizedSuggested = suggestedServer.toLowerCase();

  // Если точное совпадение, то всегда подстраиваем
  if (normalizedOriginal === normalizedSuggested) {
    return { shouldAdjust: true, reason: 'точное совпадение' };
  }

  // Если предложенный сервер начинается с оригинального слова + дефис, то подстраиваем
  if (normalizedSuggested.startsWith(normalizedOriginal + '-')) {
    return { shouldAdjust: true, reason: 'точное совпадение слова с разделителем' };
  }

  // Если предложенный сервер начинается с оригинального слова, то подстраиваем
  // НО только если оригинальное слово достаточно длинное (минимум 4 символа)
  if (normalizedSuggested.startsWith(normalizedOriginal) && normalizedOriginal.length >= 4) {
    return { shouldAdjust: true, reason: 'совпадение начала' };
  }

  // Проверяем расстояние Левенштейна
  const distance = levenshtein(normalizedOriginal, normalizedSuggested);
  const maxLen = Math.max(normalizedOriginal.length, normalizedSuggested.length);
  const normalizedDistance = distance / maxLen;

  // Если нужно изменить более 45% символов, то не подстраиваем
  if (normalizedDistance > 0.45) {
    return { shouldAdjust: false, reason: `слишком большие изменения (${(normalizedDistance * 100).toFixed(1)}%)` };
  }

  // Если оригинальное слово очень короткое (меньше 4 символов) и изменения больше 30%, то не подстраиваем
  // Для 4-символьных слов допускаем до 45% изменений
  if (normalizedOriginal.length < 4 && normalizedDistance > 0.3) {
    return { shouldAdjust: false, reason: `очень короткое слово с большими изменениями (${(normalizedDistance * 100).toFixed(1)}%)` };
  }
  if (normalizedOriginal.length === 4 && normalizedDistance > 0.45) {
    return { shouldAdjust: false, reason: `короткое слово с большими изменениями (${(normalizedDistance * 100).toFixed(1)}%)` };
  }

  // Если слово средней длины (5-7 символов) и изменения больше 35%, то не подстраиваем
  if (normalizedOriginal.length >= 5 && normalizedOriginal.length <= 7 && normalizedDistance > 0.35) {
    return { shouldAdjust: false, reason: `среднее слово с большими изменениями (${(normalizedDistance * 100).toFixed(1)}%)` };
  }

  // В остальных случаях подстраиваем
  return { shouldAdjust: true, reason: `приемлемые изменения (${(normalizedDistance * 100).toFixed(1)}%)` };
}

function findClosestSlug(server) {
  const originalServer = server;
  const normalizedServer = server.toLowerCase();

  let bestScore = -Infinity;
  let best = server;
  let debugInfo = [];

  for (const slug of slugs) {
    const normalizedSlug = slug.toLowerCase();

    // 1. Точное совпадение (игнорируя регистр)
    if (normalizedServer === normalizedSlug) {
      console.log(`[ServerMatch] Точное совпадение: "${originalServer}" → "${slug}"`);
      return slug;
    }

    // 2. Проверяем, содержится ли сервер в начале slug'а
    const startsWithMatch = normalizedSlug.startsWith(normalizedServer);

    // 3. Проверяем, содержится ли сервер как подстрока в slug'е
    const containsMatch = normalizedSlug.includes(normalizedServer);

    // 4. Расстояние Левенштейна
    const levenshteinDist = levenshtein(normalizedServer, normalizedSlug);

    // 5. Нормализованное расстояние (0-1, где 0 = идентичные, 1 = полностью разные)
    const maxLen = Math.max(normalizedServer.length, normalizedSlug.length);
    const normalizedDist = levenshteinDist / maxLen;

    // 6. Вычисляем итоговый счет (чем больше, тем лучше)
    let score = 0;

    // Проверяем точное совпадение начала с разделителем
    const exactWordMatch = normalizedSlug.startsWith(normalizedServer + '-') ||
                          normalizedSlug === normalizedServer;

    // Бонусы за совпадения
    if (exactWordMatch) {
      score += 200; // Максимальный приоритет для точного совпадения слова
    } else if (startsWithMatch) {
      score += 100; // Высокий приоритет для начала строки
    } else if (containsMatch) {
      score += 50; // Средний приоритет для подстроки
    }

    // Штраф за расстояние (чем меньше расстояние, тем больше бонус)
    score += (1 - normalizedDist) * 30;

    // Небольшой бонус за схожую длину (уменьшаем влияние)
    const lengthDiff = Math.abs(normalizedServer.length - normalizedSlug.length);
    const lengthSimilarity = 1 - (lengthDiff / maxLen);
    score += lengthSimilarity * 5; // Уменьшили с 10 до 5

    debugInfo.push({
      slug,
      exactWordMatch,
      startsWithMatch,
      containsMatch,
      levenshteinDist,
      normalizedDist: normalizedDist.toFixed(3),
      lengthSimilarity: lengthSimilarity.toFixed(3),
      score: score.toFixed(2)
    });

    if (score > bestScore) {
      bestScore = score;
      best = slug;
    }
  }

  // Логируем топ-5 кандидатов для отладки
  const topCandidates = debugInfo
    .sort((a, b) => parseFloat(b.score) - parseFloat(a.score))
    .slice(0, 5);

  console.log(`[ServerMatch] Поиск для "${originalServer}":`);
  console.log(`[ServerMatch] Топ-5 кандидатов:`, topCandidates);
  console.log(`[ServerMatch] Выбран: "${best}" (счет: ${bestScore.toFixed(2)})`);

  return best;
}

export default async function handler(req, res) {
    // Добавляем CORS заголовки для Discord плагина
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

    // Обрабатываем preflight запрос
    if (req.method === 'OPTIONS') {
      return res.status(200).end();
    }

    // 🕐 НАЧАЛО ИЗМЕРЕНИЯ ВРЕМЕНИ
    const startTime = Date.now();
    const requestId = Math.random().toString(36).substring(2, 11);

    console.log(`[${requestId}] 🚀 Запрос начат в ${new Date().toISOString()}`);

    // Ping endpoint для keep-alive (поддерживаем GET и HEAD для UptimeRobot)
    if ((req.method === 'GET' || req.method === 'HEAD') && req.query.ping === 'true') {
      const pingTime = Date.now() - startTime;
      console.log(`[${requestId}] 📡 Ping запрос обработан за ${pingTime}ms`);

      const pingResponse = {
        status: 'alive',
        function: 'send-to-sheet',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        queueSize: googleSheetsQueue.queue.length,
        responseTime: pingTime
      };

      // Для HEAD запросов не отправляем тело ответа
      if (req.method === 'HEAD') {
        res.status(200);
        res.setHeader('Content-Type', 'application/json');
        res.setHeader('X-Function-Status', 'alive');
        return res.end();
      }

      return res.status(200).json(pingResponse);
    }

    if (req.method !== 'POST') {
      const errorTime = Date.now() - startTime;
      console.log(`[${requestId}] ❌ Неверный метод ${req.method} за ${errorTime}ms`);
      return res.status(405).json({ error: 'Method not allowed' });
    }

    const { text, chat, currentUserName } = req.body;
    const parseTime = Date.now() - startTime;
    console.log(`[${requestId}] 📝 Парсинг запроса завершен за ${parseTime}ms`);
    if (!text) {
      const errorTime = Date.now() - startTime;
      console.log(`[${requestId}] ❌ Нет текста в запросе за ${errorTime}ms`);
      return res.status(400).json({ error: 'No text provided' });
    }

    console.log(`[${requestId}] 📄 Текст: "${text.substring(0, 100)}${text.length > 100 ? '...' : ''}" (${text.length} символов)`);
    console.log(`[${requestId}] 💬 Чат: ${chat}, 👤 Пользователь: ${currentUserName}`);

    // Определяем магазин до вызова ИИ
    const shopInfo = chatToShop[chat] || { shop: '', currency: 'usd' };
    const shop = shopInfo.shop;
    console.log(`[${requestId}] 🏪 Магазин: ${shop}`);

    // Парсим текст через AI (Gemini)
    console.log(`[${requestId}] 🤖 Начинаем обработку через AI...`);
    const aiStartTime = Date.now();
    let aiResult, orders, rawAiResponse, aiError, aiTime = 0;
    try {
      aiResult = await AI(text, shop);
      aiTime = Date.now() - aiStartTime;
      console.log(`[${requestId}] ✅ AI обработка завершена за ${aiTime}ms`);

      // Извлекаем данные и сырой ответ
      orders = aiResult.data;
      rawAiResponse = aiResult.rawResponse;
      console.log(`[${requestId}] 📊 AI вернул: ${Array.isArray(orders) ? orders.length : 'не массив'} заказов`);
    } catch (e) {
      aiTime = Date.now() - aiStartTime;
      console.log(`[${requestId}] ❌ AI ошибка за ${aiTime}ms:`, e.message);
      aiError = e;
    }

    // Оборачиваем одиночный объект в массив ДО проверки!
    const validationStartTime = Date.now();
    if (orders && !Array.isArray(orders) && typeof orders === 'object') {
      console.log(`[${requestId}] 🔄 Преобразуем одиночный объект в массив`);
      orders = [orders];
    }

    // Теперь проверяем валидность массива
    if (!orders || !Array.isArray(orders) || orders.length === 0) {
      const validationTime = Date.now() - validationStartTime;
      console.log(`[${requestId}] ❌ Валидация не пройдена за ${validationTime}ms`);

      // Если orders невалиден И aiError есть (значит, ошибка была выброшена из AI)
      if (aiError) {
        let errorLog = {
          error: 'AI parse error',
          aiError: aiError,
          aiErrorString: aiError?.toString?.() || String(aiError),
          aiErrorStack: aiError?.stack,
          aiErrorDetails: aiError?.details,
          aiErrorResponseText: aiError?.responseText,
          aiErrorResponse: aiError?.response,
        };
        console.error(`[${requestId}] AI parse error (caught exception):`, errorLog);
        return res.status(422).json(errorLog);
      } else {
        // Если orders невалиден, но aiError нет (значит, AI вернул undefined/null/немассив без выбрасывания ошибки)
        let errorLog = {
            error: 'AI returned invalid result',
            returnedValue: orders, // Логируем то, что вернула функция AI
            returnedValueType: typeof orders,
            returnedValueIsArray: Array.isArray(orders),
            initialText: text // Логируем исходный текст запроса
        };
        console.error(`[${requestId}] AI returned invalid result:`, errorLog);
        return res.status(422).json(errorLog);
      }
    }

    const validationTime = Date.now() - validationStartTime;
    console.log(`[${requestId}] ✅ Валидация пройдена за ${validationTime}ms: ${orders.length} заказов`);
  
    // Корректировка времени на основе расписания
    console.log(`[${requestId}] ⏰ Начинаем корректировку времени для ${orders.length} заказов...`);
    const scheduleStartTime = Date.now();

    // Получаем данные расписания из CDN кэша
    const scheduleData = await getCachedScheduleData();
    const scheduleGetTime = Date.now() - scheduleStartTime;

    if (scheduleData) {
      console.log(`[${requestId}] ✅ Данные расписания получены за ${scheduleGetTime}ms. Записей: ${scheduleData.total_records}`);
    } else {
      console.log(`[${requestId}] ⚠️ Не удалось получить данные расписания за ${scheduleGetTime}ms, продолжаем без корректировки времени`);
    }

    const adjustStartTime = Date.now();
    const ordersWithAdjustedTime = await Promise.all(orders.map((order, index) => {
      console.log(`[${requestId}] 🔄 Обрабатываем заказ ${index + 1}/${orders.length}: ${order.orderid || 'без ID'}`);
      return adjustOrderTime(order, scheduleData);
    }));
    const adjustTime = Date.now() - adjustStartTime;
    console.log(`[${requestId}] ✅ Корректировка времени завершена за ${adjustTime}ms`);

    // Постобработка: удаление пробелов из nickname и WoW- из orderid, а также обработка Gallywix и nickname-server
    console.log(`[${requestId}] 🔧 Начинаем постобработку ${ordersWithAdjustedTime.length} заказов...`);
    const postProcessStartTime = Date.now();

    const ordersWithOriginal = ordersWithAdjustedTime.map((order, index) => {
      let nickname = order.nickname ? order.nickname.replace(/\s+/g, '') : order.nickname;

      // Удаляем параметры ?utm_source=addon и ?utm_source=client из ссылок никнейма
      if (nickname && nickname.includes('?utm_source=addon')) {
        nickname = nickname.replace('?utm_source=addon', '');
        console.log(`[NicknameURL] Удален параметр ?utm_source=addon из ссылки: ${nickname}`);
      }
      if (nickname && nickname.includes('?utm_source=client')) {
        nickname = nickname.replace('?utm_source=client', '');
        console.log(`[NicknameURL] Удален параметр ?utm_source=client из ссылки: ${nickname}`);
      }

      // nickname-server обработка
      if (nickname && !nickname.toLowerCase().includes('http')) {
        const dashIdx = nickname.indexOf('-');
        if (dashIdx !== -1) {
          const name = nickname.slice(0, dashIdx);
          const server = nickname.slice(dashIdx + 1);

          // Проверяем, является ли сервер точным совпадением (игнорируя регистр)
          const normalizedServer = server.toLowerCase();
          const exactMatch = slugs.find(slug => slug.toLowerCase() === normalizedServer);
          if (exactMatch) {
            // Сервер найден в списке, заменяем на правильный регистр
            console.log(`[NicknameServer] Точное совпадение сервера: "${server}" → "${exactMatch}"`);
            nickname = `${name}-${exactMatch}`;
          } else {
            // Сервер не найден, проверяем стоит ли подстраивать
            const closest = findClosestSlug(server);
            const adjustmentCheck = shouldAdjustServer(server, closest);

            if (adjustmentCheck.shouldAdjust) {
              console.log(`[NicknameServer] Заменяем "${server}" на "${closest}" (${adjustmentCheck.reason})`);
              nickname = `${name}-${closest}`;
            } else {
              console.log(`[NicknameServer] Не подстраиваем "${server}" (${adjustmentCheck.reason}), оставляем только имя "${name}"`);
              nickname = name;
            }
          }
        }
      }
      let orderid = order.orderid;
      if (orderid) {
        // Удаляем префикс 'WoW-' если есть
        if (orderid.startsWith('WoW-')) {
          orderid = orderid.replace(/^WoW-/, '');
        }
        // Удаляем символ '#' из начала если есть
        if (orderid.startsWith('#')) {
          orderid = orderid.replace(/^#/, '');
        }
      }
      let task = order.task;
      // преобразуем bosses в строку с переносами, если это массив
      let bosses = Array.isArray(order.bosses) ? order.bosses.join('\n') : order.bosses;

      // СНАЧАЛА проверяем Gallywix (до постобработки!)
      // Gallywix Heroic
      if (
        order.task === 'LoU Single Boss HC' &&
        typeof bosses === 'string' &&
        bosses.trim() === '8) Chrome King Gallywix'
      ) {
        task = 'Gallywix Heroic';
        bosses = '';
      }
      // Gallywix Normal
      if (
        order.task === 'LoU Single Boss NM' &&
        typeof bosses === 'string' &&
        bosses.trim() === '8) Chrome King Gallywix'
      ) {
        task = 'Gallywix Normal';
        bosses = '';
      }

      // Дополнительная очистка bosses если task уже Gallywix (пришло от ИИ)
      if (task === 'Gallywix Heroic' || task === 'Gallywix Normal') {
        bosses = '';
      }

      // ПОТОМ применяем постобработку bosses (только если bosses не пустое после Gallywix)
      // Преобразуем формат из "6) The One-Armed Bandit, 7) Mug'Zee, Heads of Security"
      // в "6)[The One-Armed Bandit]7)[Mug'Zee, Heads of Security]"
      if (typeof bosses === 'string' && bosses.trim()) {
        // Используем более простой подход: ищем все паттерны "номер) название" до следующего номера или конца строки
        bosses = bosses.replace(/(\d+\))\s*(.+?)(?=\s*\d+\)|$)/g, (_, number, name) => {
          // Убираем лишние пробелы и запятые в начале и конце названия
          const cleanName = name.trim().replace(/^,\s*/, '').replace(/,\s*$/, '');
          return `${number}[${cleanName}]`;
        });
      }
      // Используем уже определенные выше shopInfo и shop
      const currency = shopInfo.currency;

      let price = order.price;
      if (typeof price === 'string') {
        price = price.replace(',', '.');
        price = isNaN(Number(price)) ? '' : Number(price);
      }

      // Создаем объект с валютными полями
      const priceFields = {
        priceUsd: '',
        priceEur: '',
        priceRub: ''
      };

      // Устанавливаем цену в соответствующее валютное поле
      if (price !== '' && price !== null && price !== undefined) {
        switch (currency) {
          case 'usd':
            priceFields.priceUsd = price;
            break;
          case 'eur':
            priceFields.priceEur = price;
            break;
          case 'rub':
            priceFields.priceRub = price;
            break;
          default:
            priceFields.priceUsd = price; // по умолчанию USD
        }
      }

      // Добавляем geminiComment, reaction_user и rawAiResponse
      const geminiComment = order.bossComment || ''; // по умолчанию пустое (ячейка AP)
      const reaction_user = currentUserName || ''; // пользователь от которого пришло сообщение
      const aiRawResponse = rawAiResponse || ''; // сырой ответ от ИИ (ячейка AQ)

      return { ...order, original: text, nickname, orderid, task, bosses, shop, geminiComment, reaction_user, aiRawResponse, ...priceFields };
    });

    const postProcessTime = Date.now() - postProcessStartTime;
    console.log(`[${requestId}] ✅ Постобработка завершена за ${postProcessTime}ms`);

    // Отправляем массив заказов через очередь Google Sheets API
    console.log(`[${requestId}] 📤 Добавляем ${ordersWithOriginal.length} заказов в очередь Google Sheets API...`);
    const queueStartTime = Date.now();

    try {
      const result = await googleSheetsQueue.add(ordersWithOriginal);

      // Время записи в Google Sheets (без wowHetz) - используем время окончания основной обработки
      const queueTime = result.mainProcessingEndTime ? (result.mainProcessingEndTime - queueStartTime) : (Date.now() - queueStartTime);

      // Общее время заканчивается после записи в Google Sheets (до wowHetz)
      // Используем время окончания основной обработки из Google Sheets API
      const totalTime = result.mainProcessingEndTime ? (result.mainProcessingEndTime - startTime) : (Date.now() - startTime);

      console.log(`[${requestId}] ✅ Данные записаны через Google Sheets API за ${queueTime}ms`);
      console.log(`[${requestId}] 🎉 ОБЩЕЕ ВРЕМЯ ОБРАБОТКИ: ${totalTime}ms`);
      console.log(`[${requestId}] 📊 РАЗБИВКА ВРЕМЕНИ:`);
      console.log(`[${requestId}]   • Парсинг запроса: ${parseTime}ms`);
      console.log(`[${requestId}]   • AI обработка: ${aiTime}ms`);
      console.log(`[${requestId}]   • Валидация: ${validationTime}ms`);
      console.log(`[${requestId}]   • Получение расписания: ${scheduleGetTime}ms`);
      console.log(`[${requestId}]   • Корректировка времени: ${adjustTime}ms`);
      console.log(`[${requestId}]   • Постобработка: ${postProcessTime}ms`);
      console.log(`[${requestId}]   • Запись в Google Sheets: ${queueTime}ms`);

      // wowHetz выполняется отдельно и логируется отдельно
      if (result.wowHetzResults && result.wowHetzResults.length > 0) {
        const successful = result.wowHetzResults.filter(r => r.result.success).length;
        const failed = result.wowHetzResults.filter(r => !r.result.success).length;
        if (result.timing && result.timing.wowHetz === 'written-parallel') {
          console.log(`[${requestId}] 🔮 wowHetz выполнен ПАРАЛЛЕЛЬНО: ${successful} успешно, ${failed} ошибок`);
        } else {
          console.log(`[${requestId}] 🔮 wowHetz выполнен отдельно: ${successful} успешно, ${failed} ошибок`);
        }
      }

      return res.status(200).json({
        ok: true,
        method: 'google-sheets-api-queue',
        ordersProcessed: result.ordersProcessed,
        lastRow: result.lastRow,
        queueMethod: result.method || 'standard',
        timing: {
          total: totalTime,
          parsing: parseTime,
          ai: aiTime,
          validation: validationTime,
          schedule: scheduleGetTime,
          timeAdjustment: adjustTime,
          postProcessing: postProcessTime,
          googleSheets: queueTime
        }
      });
    } catch (error) {
      // При ошибке используем текущее время (wowHetz не выполнялся)
      const queueTime = Date.now() - queueStartTime;
      const totalTime = Date.now() - startTime;

      console.error(`[${requestId}] ❌ Ошибка Google Sheets API за ${queueTime}ms:`, error.message);
      console.error(`[${requestId}] 📊 Время до ошибки: ${totalTime}ms`);

      return res.status(500).json({
        error: error.message,
        stack: error?.stack,
        method: 'google-sheets-api-queue',
        timing: {
          total: totalTime,
          parsing: parseTime,
          googleSheetsError: queueTime
        }
      });
    }
  }