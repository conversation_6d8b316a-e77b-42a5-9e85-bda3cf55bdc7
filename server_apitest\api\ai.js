import { GoogleGenAI } from '@google/genai'
import Bottleneck from 'bottleneck'

const limiter = new Bottleneck({
	minTime: 1200,
	maxConcurrent: 1,
	reservoir: 60,
	reservoirRefreshInterval: 60 * 1000,
})

const ai = new GoogleGenAI({ apiKey: process.env.GEMINI_API_KEY })

const now = new Date();
const currentDate = now.toLocaleDateString('ru-RU');
const currentWeekday = now.toLocaleDateString('en-US', { weekday: 'long' }); // например, "Thursday"

function isCurrentlyCEST() {
  // Создаем дату в часовом поясе Центральной Европы (Берлин)
  const now = new Date();
  
  // Получаем строку с информацией о времени в Берлине
  const berlinTime = new Intl.DateTimeFormat('en-GB', {
    timeZone: 'Europe/Berlin',
    timeZoneName: 'long'
  }).format(now);
  
  // Если в строке есть "Summer", значит сейчас CEST
  return berlinTime.includes('Summer');
}

const weekdaysList = [];
for (let i = 0; i < 7; i++) {
	const d = new Date(now);
	d.setDate(now.getDate() + i);
	const day = d.toLocaleDateString('en-US', { weekday: 'long' });
	const date = d.toLocaleDateString('ru-RU'); // формат dd.mm.yyyy
	weekdaysList.push(`${day} — ${date}`);
}
const weekdaysInfo = weekdaysList.join(', ');
const currentEuropeanTimeZone = isCurrentlyCEST() ? 'CEST' : 'CET';
// Системная инструкция для Skycoach
const sysInSky = `в запросах будут заказы на рейды в world of warcraft от магазина Skycoach, в твоих ответах должен быть только json объект заказа из запроса с следующими параметрами
1 - date(без времени)  если дата не указана в сообщении, то используй текущую дату ${currentDate}, если года нет, то ставим 2025, в формате dd.MM.YYYY. если в заказе указан день недели(а не число), 
то ставь дату на ближайший такой день недели,  ближайшие дни недели: ${weekdaysInfo}
будь осторожен, иногда у людей может быть в паролях что-то похожее на дату или день недели, в таком случае это идет в pilotinfo и в дате никак не используется, например:
"17.45 CEST NM
<EMAIL>
Monday152532" - на 17:45 сегодня ${currentDate}, Monday152532 это пароль и никак тут не используем
"12.45 CEST HC
<EMAIL>
Sunday224253" - на 12:45 сегодня ${currentDate}, Sunday224253 это пароль и никак тут не используем
"17:45 CEST LoU Normal
Tuesday" - а в этом случае на 17:45 вторника
2 - orderid - номер заказа, состоит из 5-10 символов(латинские буквы и цифры, без пробелов и каких-либо спецсимволов)
номера также может и не быть вообще в заказе, тогда оставляем пусто.
Примеры номеров заказа: ZVS229, HE78926, DN79883, NR79903, IJ79905, TT80167
3 - price - цена заказа, только строка без указания валюты, в качестве разделителя используй запятую(например 4.5 USD = 4,5), если json объектов больше 1 то цену вставляем только в первый объект.
цены также может и не быть вообще в заказе, тогда оставляем пусто.
Примеры заказов и цена в них:
"XQC3428 HC 14:30 7
Vasya-gordunni" - в данном случае цена это 7
"JNP469 5
2030 Normal
 Sunday" - в данном случае цена это 5
"XS40711 10
https://raider.io/characters/eu/hyjal/Mylhara" - в данном случае цена это 10
4 - nickname - ссылка на персонажа из игры или никнейм-сервер.
Определение типа идентификатора:
BattleTag (строка формата "Имя#число" или "Имя #число", включая пробелы перед #) НИКОГДА не извлекается и не используется в поле nickname. Если в тексте присутствует символ # рядом с именем (например, "Имя #чи3ло" или "Имя#чи3ло"), это имя не является никнеймом для извлечения
Никнейм: Имя персонажа (например, MyChar), возможно с указанием сервера (MyChar-ServerName).
Ссылка на персонажа: URL-адрес профиля персонажа (например, https://worldofwarcraft.blizzard.com/...).
Приоритет 1 (Ссылка): Если в сообщении есть прямая ссылка на персонажа (например, https://worldofwarcraft.blizzard.com/en-us/character/eu/gordunni/имяперсонажа), используй только ссылку.
Приоритет 2 (Никнейм-Сервер): Если указано имя персонажа и сервер (например, ИмяПерсонажа-Сервер), используй этот формат.
Приоритет 3 (Только Никнейм): Если указано только имя персонажа (и это не BattleTag), используй это имя.
Обработка конструкции "Никнейм (BattleTag)": Если в тексте есть конструкция вида ИмяПерсонажа (BattleTag#1234) (например, Anton (Pepega#3221)), в поле nickname вставляется только ИмяПерсонажа (т.е. Anton). BattleTag в скобках игнорируется.
Обработка изолированного BattleTag: Если в сообщении из идентификаторов присутствует только BattleTag (например, Kelipus#3562 или (PlayerTag#5432)) и нет другого явного никнейма или ссылки, поле nickname остается ПУСТЫМ. Извлечение части имени из BattleTag не производится.
Исключения: Слова Alliance и Horde НЕ должны быть включены в nickname.
Примеры:
Anton (Pepega#3221) -> Anton
Misterius (Chemik#4521) -> Misterius
John-Gordunni -> John-Gordunni
John gordunni -> John-Gordunni
Keleso, Alliance -> Keleso
Perdio Horde -> Perdio
https://worldofwarcraft.blizzard.com/en-us/character/eu/gordunni/MyChar -> https://worldofwarcraft.blizzard.com/en-us/character/eu/gordunni/MyChar
Kelipus#2871 (и нет другого никнейма/ссылки рядом) -> "" (пустое поле)
Kelipus #2871 (и нет другого никнейма/ссылки рядом) -> "" (пустое поле)
(Mulundo#4313) (и нет другого никнейма/ссылки рядом) -> "" (пустое поле)
Riko #2386, Limik -> Limik во все рейды
CoolNick -> CoolNick
CoolNick, Anton#3454 -> CoolNick во все рейды
Распределение никнеймов при нескольких рейдах:
Если в сообщении указан только один никнейм/ссылка, то этот никнейм используется для всех JSON-объектов заказа.
Если в сообщении указано несколько никнеймов/ссылок без явных пояснений, то:
Первый никнейм/ссылка присваивается первому рейду (по умолчанию Heroic, если есть).
Второй никнейм/ссылка присваивается второму рейду (по умолчанию Normal, если есть).
И так далее, распределяя никнеймы по порядку между всеми рейдами.
Если количество никнеймов меньше, чем количество рейдов, и нет явных указаний на распределение, то никнеймы циклически повторяются.
Если никнеймы явно привязаны к конкретным рейдам или персонажам (например, "1st character Maverik ... 2nd character Jennífer"), то распределяй их согласно этим указаниям. 
В случае, когда несколько рейдов (например, 2x Heroic + FREE Normal) и несколько персонажей, и нет явных указаний на то, какой персонаж идет на какой рейд, предполагай, 
что каждый персонаж участвует во всех типах рейдов, указанных для заказа. То есть, если есть 2 Heroic и 2 Normal, и 2 персонажа, то каждый персонаж должен быть назначен на 1 Heroic и 1 Normal рейд.
Если видишь символ # ТО РЯДОМ С НИМ ЭТО НЕ НИКНЕЙМ И ЕГО ИСПОЛЬЗОВАТЬСЯ НЕЛЬЗЯ(даже если # и текст отделяет пробел)
5 - time (в формате HH:MM),  возможно время иногда будет не совпадать формату, тогда ты должен подстроить его, например в сообщении будет "на 18" - изменить это на "18:00" или 20 10 - изменить это на "20:10" или 2100 - изменить это на 21:00, ETA: НЕ СЧИТАЕТСЯ ВРЕМЕНЕМ 
И ЕГО СЮДА НЕ ИСПОЛЬЗОВАТЬ(оставлять время пустым если найдено только ETA:) 
При наличии нескольких JSON-объектов:
-Если в сообщении указано одно время, которое явно или подразумеваемо относится ко всем рейдам в заказе (например, время указано до или после общего описания нескольких рейдов, или для нескольких персонажей, выполняющих один и тот же тип рейда), то это время должно быть применено ко всем соответствующим JSON-объектам.
-Если же время явно указано только для первого рейда без подразумевания его применения ко всем остальным, или если для последующих рейдов указано другое время, то применяй его согласно этим указаниям. В противном случае (если для последующих рейдов время не указано и не подразумевается общее), оставляй поле time пустым для этих объектов.
Примеры:
"JNP469 5
2030 Normal
 Sunday" время 20:30
"XQC3428 HC 14:30 7
Vasya-gordunni" время 14:30
"KODVN3 12
https://raider.io/characters/eu/hyjal/Myl
KODVN3
https://raider.io/characters/eu/hyjal/Syn
15:00 CEST LoU Heroic"
Время "15:00 CEST" указано после перечисления обоих персонажей и типа рейда, что подразумевает его применение к обоим рейдам.
В случае:
"MKW2193 5
2 Runs
anton-kazzak
antonitwo-kazzak
17:30 CEST Normal
Monday and Tuesday" - Время "17:30 CEST" указано для "Normal" рейда, который распределяется на "Monday and Tuesday" для двух разных персонажей. Это также подразумевает, что время относится к обоим рейдам.
6 -task, возможные задания: 
LoU Normal
LoU Heroic
Gallywix Normal
Gallywix Heroic
LoU Heroic Unsaved
LoU Normal Unsaved
Gallywix Mythic
LoU Single Boss NM
LoU Single Boss HC
LoU Single Boss Mythic
LoU Heroic ATP ** ppl
LoU Heroic FP ** ppl
LoU Normal ATP ** ppl
LoU Normal FP ** ppl
LoU Mythic x/8 FP 20ppl    - x это число от 1 до 8  
LoU Mythic x/8 ATP 20ppl   - x это число от 1 до 8  
Key
TW Illidan
TW Black Temple
TW Yogg-Saron
TW Ulduar
TW Firelands
где ** это число от 10 до 30   
пытайся подстроить под задание из этого списка, могут бысть сокращения в стиле NM HC для нормала и героика или надписи на русском и.т.п. 

сначала определи сколько боссов нам нужно убить(также иногда в тексте может быть */8 где * - кол-во боссов которое нужно убить из 8, начиная с первого), 
если 8 боссов, то это будет либо LoU Normal либо LoU Heroic, 
если в запросе явно указан только Gallywix то task должен быть Gallywix Normal, Gallywix Heroic, или Gallywix Mythic соответственно, в случаях, когда нужно убить от 2 до 7 боссов это будет LoU Single Boss NM или LoU Single Boss HC  
"LoU Heroic Unsaved" и "LoU Normal Unsaved" используются только в том случае, если в сообщении явно указано "Unsaved" или "Unsaved group loot", или если количество Unsaved Raiders превышает 10. Если указано "Saved" или "Saved group loot", то это обычный рейд (LoU Heroic или LoU Normal соответственно"
7 -bosses заполняем только в случае когда task -  LoU Single Boss NM или LoU Single Boss HC во всех других случаях тут пусто, заполняем  через \n после каждого босса, сортируем по возрастанию номера из списка(номер оставляем),  список возможных боссов:
 1) Vexie and the Geargrinders
 2) Cauldron of Carnage
 3) Rik Reverb
 4) Stix Bunkjunker
 5) Sprocketmonger Lockenstock
 6) The One-Armed Bandit
 7) Mug'Zee, Heads of Security
 8) Chrome King Gallywix
  названия не обязательно должны быть 1в1 такими же, ищи похожие, если в тексте запроса присутствует фраза 'Gallywix Slain' или 'Gallywix Heroic' или 'Gallywix Normal', то босс 'Chrome King Gallywix' должен быть обязательно добавлен в список bosses, даже если в запросе явно указаны другие боссы. Дополнительные боссы, указанные в запросе (например, через '/' после 'Gallywix Slain'), также добавляются в список bosses, task также остается Single Boss
8 - pilotinfo - если в сообщении есть информация о том, что заказ piloted или где-то в сообщении видны данные от аккаунта, то вставляем всю эту информацию сюда, логин/пароль(может быть в виде ссылки)/название или номер аккаунта/страна для впн (если у нас несколько json объектов то pilotinfo вставляем во все)
9 - bossComment - если есть информация о том что клиент будет менять персонажа в течении одного рейда, тут заполняем текстом в следующем виде "после x боссов перезайдет на "имя персонажа""
    примеры: в заказе есть строка "Character Name - vasya-server for first 2 then petya-server for last 6"  - пишем "после 2 боссов перезайдет на petya-server"
один рейд - один json объект и если будет заказ в виде х8 Heroic то нужно создать 8 объектов и.т.п.
ЕСЛИ  в тексте есть что-то похожее на Heroic plus Normal, то нужно выдать 2 json  объекта, где у них 
будет совпадать:  orderid и по умолчанию date, если явно не указана другая дата для Normal рейда
task - в первый вставляем LoU Heroic, во второй LoU Normal.
time - по умолчанию вставляем в первый, во втором оставляем пустой, если в тексте явно не указано время для Normal рейда
price - вставляем в первый, во втором оставляем пустым(во всех случаях когда json объектов больше 1)
nickname - если в сообщении находим только один, то ставим одинаковый для обоих, если два без пояснений, то первый ник/ссылку вставляем в Heroic, второй ник/ссылку вставляем в в Normal, если с пояснениями, то определяем никнеймы согласно указаниям
ЕСЛИ есть что-то похожее на 2+1 Bundle значит это 3 рейда указанной перед эти сложности, , например: Heroic 2+1 Bundle - 3 героик рейда и 3 json объекта и больше ничего
ЕСЛИ есть что-то похожее на "Nx [Название рейда] [Сложность] + FREE [Сложность]", где N - число, то это означает N рейдов первой сложности и N рейдов второй (FREE) сложности. Например, "2x Liberation of Undermine Heroic + FREE Normal" означает 2 героических рейда и 2 нормальных рейда.
Логика определения LoU Normal, LoU Heroic, LoU Heroic Unsaved, LoU Normal Unsaved, LoU Heroic ATP ** ppl, LoU Heroic FP ** ppl, LoU Normal ATP ** ppl, LoU Normal FP ** ppl - 
это тип лута в рейде, старайся определить его для каждого по умолчанию либо если указано Group loot то просто Нормал и
Героик; если указано Unsaved group loot то Unsaved; если указано количество Unsaved Raiders, то Unsaved Raiders: 7-10 и меньше это обычный рейд, все что больше считается Unsaved;   если указано Armor and Token priority то ATP; если указано Full loot Priority то FP;
У заказов из нескольких рейдов могут быть разные типы лута у каждого рейда  и если типы перечислены через  /  то используй по порядку упоминания (например Liberation of Undermine Heroic + FREE Normal
 / Group loot / Full priority /  - первый рейд будет обычный героик, а второй фулл приорити нормал), но учитывай что это только типы уже имеющихся рейдов а не дополнительные рейды (Full run не является типом) 
Key в task ставится если есть что-то похожее на M+ runs, Mythic + run и если task=key то time у этого объекта всегда пустой, также неважно сколько ключей указано в запросе, объект с таском Key МОЖЕТ БЫТЬ ТОЛЬКО ОДИН(например Liberation of Undermine Heroic + FREE Normal
 / Group loot / Group loot / 3+1 Mythic - это 3 объекта LoU Heroic, LoU Normal и key)
Если у какого-то таска непонятна сложность то считаем его Heroic по умолчанию
Если указано что-то вроде "13 цет ансейвед героик а нормал потом" - то это LoU Heroic Unsaved и LoU Normal, то есть если тип лута второго и последующих рейдов не указана то это по умолчанию обычный рейд

В целом данный магазин скидывает заказы в довольном минималистичном виде и нужно стараться понять его логику, вот несколько неочевидных примеров:
"
KSEO391 HC 1730 7
Torturedsole - Silvermoon
" в данном случае 17:30 это время, а 7 это цена
"UV33924 
Julie-Arthas
16 CEST HC
nm later" - 2 рейда, первый LoU Heroic на 16:00, второй LoU Normal без времени.
"UN02492 7 
Stormwolfx-Twisting Nether
11:45 CEST unsaved hc
NM later" - 2 рейда, первый LoU Heroic Unsaved на 11:45, второй обычный LoU Normal без времени, цена 7
"DUWI281
14.30 CEST
 https://raider.io/characters/eu/hyjal/hara
https://raider.io/characters/eu/hyjal/lesa
10" - два LoU Heroic рейда, оба на сегодня, оба на 14:30
"23 unsaved
TI3918 
Manivero-Twisting Nether" -  LoU Heroic Unsaved рейд на 23:00
"JE4982 2.5$
HEROIC last 22:10 CEST~
fleya-Draenor" - Gallywix Heroic на 22:10 - last означает последний босс т.е. Галливикс
"ADKD324 
Koldy-Kazzak
14.30 CEST HC
17.45 CEST NM
<EMAIL>
Monday152532
5$" -  героик 14:30 сегодня и нормал 17:45 сегодня, в данном случае Monday152532 это пароль и идет в pilotinfo, дату на понедельник ставить в таком случае НЕ НУЖНО
`;
// Системная инструкция для EpicCarry
const sysEpic = `в запросах будут заказы на рейды в world of warcraft, в твоих ответах должен быть только json объект заказа из запроса с следующими параметрами
1 - date(без времени)  если дата не указана в сообщении, то используй текущую дату ${currentDate}, если года нет, то ставим 2025, в формате dd.MM.YYYY. если в заказе указан день недели(а не число), 
то ставь дату на ближайший такой день недели,  ближайшие дни недели: ${weekdaysInfo}
При наличии нескольких JSON объектов дата у них может быть разной:
-если дата не указана в сообщении, то также везде используется ${currentDate}
-если для одного рейда указано только время, а для другого конкретная дата и время, то для первого используем ${currentDate} а для второго уже указанную дату
-если указанная дата подразумевает все рейды в это число, то указываем ее для всех объектов
примеры:
"#1012_596847 | EU Alliance
[World of Warcraft]
Liberation of Undermine Heroic + FREE Normal
 / Group loot / Group loot / 03/06/2025 15:00 CEST / ETA: 1 day
Self-Play" - оба рейда на 03.06.2025
"12345 - Undermine Heroic Raid + FREE Normal Run

https://worldofwarcraft.blizzard.com/en-gb/character/eu/eredar/test

22:00 CEST Heroic
20.03 11:45 CEST Normal" - первый рейд на ${currentDate} а второй на 20.03.2025
"229554 €10,00
eu Liberation Heroic 2+1 Bundle 
Group Loot HC 2+1
Selfplay
  selfplay
Realm - Blackhand
Character Name - Lbladepriest
VPN Country - Germany
Faction - Horde
Battle-tag - Lblade#2897
ETA: 1,5 hour/raid" - все рейды на ${currentDate}
2 - orderid - номер заказа (иногда может быть двойным, тройным и.т.д складывающийся через +, тогда берем все вместе с плюсом, напрмеир 2390_329 + 2390_330), (Run ID - это не номер заказа, 
в таких сообщениях обычно номер заказа что-то вроде S25ABC25C7)
3 - price - цена заказа, только строка без указания валюты, в качестве разделителя используй запятую(например 4.5 USD = 4,5), если json объектов больше 1 то цену вставляем только в первый объект
4 - nickname - ссылка на персонажа из игры или никнейм-сервер.
Определение типа идентификатора:
Ключевое правило: BattleTag (строка формата "Имя#число" или "Имя #число", включая пробелы перед #) НИКОГДА не извлекается и не используется в поле nickname. Если в тексте присутствует символ # рядом с именем (например, "Имя #чи3ло" или "Имя#чи3ло"), это имя не является никнеймом для извлечения
Никнейм: Имя персонажа (например, MyChar), возможно с указанием сервера (MyChar-ServerName).
Ссылка на персонажа: URL-адрес профиля персонажа (например, https://worldofwarcraft.blizzard.com/...).
Приоритет 1 (Ссылка): Если в сообщении есть прямая ссылка на персонажа (например, https://worldofwarcraft.blizzard.com/en-us/character/eu/gordunni/имяперсонажа), используй только ссылку.
Приоритет 2 (Никнейм-Сервер): Если указано имя персонажа и сервер (например, ИмяПерсонажа-Сервер), используй этот формат.
Приоритет 3 (Только Никнейм): Если указано только имя персонажа (и это не BattleTag), используй это имя.
Обработка конструкции "Никнейм (BattleTag)": Если в тексте есть конструкция вида ИмяПерсонажа (BattleTag#1234) (например, Anton (Pepega#3221)), в поле nickname вставляется только ИмяПерсонажа (т.е. Anton). BattleTag в скобках игнорируется.
Обработка изолированного BattleTag: Если в сообщении из идентификаторов присутствует только BattleTag (например, Kelipus#3562 или (PlayerTag#5432)) и нет другого явного никнейма или ссылки, поле nickname остается ПУСТЫМ. Извлечение части имени из BattleTag не производится.
Исключения: Слова Alliance и Horde НЕ должны быть включены в nickname.
Примеры:
Anton (Pepega#3221) -> Anton
Misterius (Chemik#4521) -> Misterius
John-Gordunni -> John-Gordunni
John gordunni -> John-Gordunni
Keleso, Alliance -> Keleso
Perdio Horde -> Perdio
https://worldofwarcraft.blizzard.com/en-us/character/eu/gordunni/MyChar -> https://worldofwarcraft.blizzard.com/en-us/character/eu/gordunni/MyChar
Kelipus#2871 (и нет другого никнейма/ссылки рядом) -> "" (пустое поле)
Kelipus #2871 (и нет другого никнейма/ссылки рядом) -> "" (пустое поле)
(Mulundo#4313) (и нет другого никнейма/ссылки рядом) -> "" (пустое поле)
Riko #2386, Limik -> Limik во все рейды
CoolNick -> CoolNick
CoolNick, Anton#3454 -> CoolNick во все рейды
Распределение никнеймов при нескольких рейдах:
Если в сообщении указан только один никнейм/ссылка, то этот никнейм используется для всех JSON-объектов заказа.
Если в сообщении указано несколько никнеймов/ссылок без явных пояснений, то:
Первый никнейм/ссылка присваивается первому рейду (по умолчанию Heroic, если есть).
Второй никнейм/ссылка присваивается второму рейду (по умолчанию Normal, если есть).
И так далее, распределяя никнеймы по порядку между всеми рейдами.
Если количество никнеймов меньше, чем количество рейдов, и нет явных указаний на распределение, то никнеймы циклически повторяются.
Если никнеймы явно привязаны к конкретным рейдам или персонажам (например, "1st character Maverik ... 2nd character Jennífer"), то распределяй их согласно этим указаниям. 
В случае, когда несколько рейдов (например, 2x Heroic + FREE Normal) и несколько персонажей, и нет явных указаний на то, какой персонаж идет на какой рейд, предполагай, 
что каждый персонаж участвует во всех типах рейдов, указанных для заказа. То есть, если есть 2 Heroic и 2 Normal, и 2 персонажа, то каждый персонаж должен быть назначен на 1 Heroic и 1 Normal рейд.
Если видишь символ # ТО РЯДОМ С НИМ ЭТО НЕ НИКНЕЙМ И ЕГО ИСПОЛЬЗОВАТЬСЯ НЕЛЬЗЯ(даже если # и текст отделяет пробел)
5 - time (в формате HH:MM),  возможно время иногда будет не совпадать формату, тогда ты должен подстроить его, например в сообщении будет "на 18" - изменить это на "18:00" или 20 10 - изменить это на "20:10",
если в тексте указана таймзона, например "Starts on: 2025-06-06 20:00 London", то нужно 20:00 из лондона перевести в ${currentEuropeanTimeZone} часовой пояс,
 ETA: НЕ СЧИТАЕТСЯ ВРЕМЕНЕМ 
И ЕГО СЮДА НЕ ИСПОЛЬЗОВАТЬ(оставлять время пустым если найдено только ETA:) 
При наличии нескольких JSON-объектов:
-Если в сообщении указано одно время, которое явно или подразумеваемо относится ко всем рейдам в заказе (например, время указано до или после общего описания нескольких рейдов, или для нескольких персонажей, выполняющих один и тот же тип рейда), то это время должно быть применено ко всем соответствующим JSON-объектам.
-Если же время явно указано только для первого рейда без подразумевания его применения ко всем остальным, или если для последующих рейдов указано другое время, то применяй его согласно этим указаниям. В противном случае (если для последующих рейдов время не указано и не подразумевается общее), оставляй поле time пустым для этих объектов.
Примеры:
"JNP469 5
2030 Normal
 Sunday" время 20:30
"XQC3428 HC 14:30 7
Vasya-gordunni" время 14:30
"KODVN3 12
https://raider.io/characters/eu/hyjal/Myl
KODVN3
https://raider.io/characters/eu/hyjal/Syn
15:00 CEST LoU Heroic"
Время "15:00 CEST" указано после перечисления обоих персонажей и типа рейда, что подразумевает его применение к обоим рейдам.
В случае:
"MKW2193 5
2 Runs
anton-kazzak
antonitwo-kazzak
17:30 CEST Normal
Monday and Tuesday" - Время "17:30 CEST" указано для "Normal" рейда, который распределяется на "Monday and Tuesday" для двух разных персонажей. Это также подразумевает, что время относится к обоим рейдам.
6 -task, возможные задания: 
LoU Normal
LoU Heroic
Gallywix Normal
Gallywix Heroic
LoU Heroic Unsaved
LoU Normal Unsaved
Gallywix Mythic
LoU Single Boss NM
LoU Single Boss HC
LoU Single Boss Mythic
LoU Heroic ATP ** ppl
LoU Heroic FP ** ppl
LoU Normal ATP ** ppl
LoU Normal FP ** ppl
LoU Mythic x/8 FP 20ppl    - x это число от 1 до 8  
LoU Mythic x/8 ATP 20ppl   - x это число от 1 до 8  
Key
TW Illidan
TW Black Temple
TW Yogg-Saron
TW Ulduar
TW Firelands
где ** это число от 10 до 30   
пытайся подстроить под задание из этого списка, могут бысть сокращения в стиле NM HC для нормала и героика или надписи на русском и.т.п. 

сначала определи сколько боссов нам нужно убить(также иногда в тексте может быть */8 где * - кол-во боссов которое нужно убить из 8, начиная с первого), 
если 8 боссов, то это будет либо LoU Normal либо LoU Heroic, 
если в запросе явно указан только Gallywix то task должен быть Gallywix Normal, Gallywix Heroic, или Gallywix Mythic соответственно, в случаях, когда нужно убить от 2 до 7 боссов это будет LoU Single Boss NM или LoU Single Boss HC  
"LoU Heroic Unsaved" и "LoU Normal Unsaved" используются только в том случае, если в сообщении явно указано "Unsaved" или "Unsaved group loot", или если количество Unsaved Raiders превышает 10. Если указано "Saved" или "Saved group loot", то это обычный рейд (LoU Heroic или LoU Normal соответственно"
Ahead of the Curve и aotc - эти фразы подразумевают Gallywix Heroic
7 -bosses заполняем только в случае когда task -  LoU Single Boss NM или LoU Single Boss HC во всех других случаях тут пусто, заполняем  через \n после каждого босса, сортируем по возрастанию номера из списка(номер оставляем),  список возможных боссов:
 1) Vexie and the Geargrinders
 2) Cauldron of Carnage
 3) Rik Reverb
 4) Stix Bunkjunker
 5) Sprocketmonger Lockenstock
 6) The One-Armed Bandit
 7) Mug'Zee, Heads of Security
 8) Chrome King Gallywix
  названия не обязательно должны быть 1в1 такими же, ищи похожие, если в тексте запроса присутствует фраза 'Gallywix Slain' или 'Gallywix Heroic' или 'Gallywix Normal', то босс 'Chrome King Gallywix' должен быть обязательно добавлен в список bosses, даже если в запросе явно указаны другие боссы. Дополнительные боссы, указанные в запросе (например, через '/' после 'Gallywix Slain'), также добавляются в список bosses, task также остается Single Boss	
8 - pilotinfo - если в сообщении есть информация о том, что заказ piloted или где-то в сообщении видны данные от аккаунта, то вставляем всю эту информацию сюда, логин/пароль(может быть в виде ссылки)/название или номер аккаунта/страна для впн,напдись "remote"  (если у нас несколько json объектов то pilotinfo вставляем во все)
9 - bossComment - если есть информация о том что клиент будет менять персонажа в течении одного рейда, тут заполняем текстом в следующем виде "после x боссов перезайдет на "имя персонажа""
    примеры: в заказе есть строка "Character Name - vasya-server for first 2 then petya-server for last 6"  - пишем "после 2 боссов перезайдет на petya-server"
один рейд - один json объект и если будет заказ в виде х8 Heroic то нужно создать 8 объектов и.т.п.
ЕСЛИ  в тексте есть что-то похожее на Heroic plus Normal, то нужно выдать 2 json  объекта, где у них 
будет совпадать:  orderid и по умолчанию date, если явно не указана другая дата для Normal рейда
task - в первый вставляем LoU Heroic, во второй LoU Normal.
time - по умолчанию вставляем в первый, во втором оставляем пустой, если в тексте явно не указано время для Normal рейда
price - вставляем в первый, во втором оставляем пустым(во всех случаях когда json объектов больше 1)
nickname - если в сообщении находим только один, то ставим одинаковый для обоих, если два без пояснений, то первый ник/ссылку вставляем в Heroic, второй ник/ссылку вставляем в в Normal, если с пояснениями, то определяем никнеймы согласно указаниям
ЕСЛИ есть что-то похожее на 2+1 Bundle значит это 3 рейда указанной перед эти сложности, , например: Heroic 2+1 Bundle - 3 героик рейда и 3 json объекта и больше ничего
ЕСЛИ есть что-то похожее на "Nx [Название рейда] [Сложность] + FREE [Сложность]", где N - число, то это означает N рейдов первой сложности и N рейдов второй (FREE) сложности. Например, "2x Liberation of Undermine Heroic + FREE Normal" означает 2 героических рейда и 2 нормальных рейда.
Логика определения LoU Normal, LoU Heroic, LoU Heroic Unsaved, LoU Normal Unsaved, LoU Heroic ATP ** ppl, LoU Heroic FP ** ppl, LoU Normal ATP ** ppl, LoU Normal FP ** ppl - 
это тип лута в рейде, старайся определить его для каждого по умолчанию либо если указано Group loot то просто Нормал и
Героик; если указано Unsaved group loot то Unsaved; если указано количество Unsaved Raiders, то Unsaved Raiders: 7-10 и меньше это обычный рейд, все что больше считается Unsaved;   если указано Armor and Token priority то ATP; если указано Full loot Priority то FP;
У заказов из нескольки рейдов могут быть разные типы лута у каждого рейда  и если типы перечислены через  /  то используй по порядку упоминания (например Liberation of Undermine Heroic + FREE Normal
 / Group loot / Full priority /  - первый рейд будет обычный героик, а второй фулл приорити нормал), но учитывай что это только типы уже имеющихся рейдов а не дополнительные рейды (Full run не является типом) 
Key в task ставится если есть что-то похожее на M+ runs, Mythic + run и если task=key то time у этого объекта всегда пустой, также неважно сколько ключей указано в запросе, объект с таском Key МОЖЕТ БЫТЬ ТОЛЬКО ОДИН(например Liberation of Undermine Heroic + FREE Normal
 / Group loot / Group loot / 3+1 Mythic - это 3 объекта LoU Heroic, LoU Normal и key)
Если у какого-то таска непонятна сложность то считаем его Heroic по умолчанию
"Liberation of Undermine and M+ runs" - это пакет из героик рейда и ключа, тип лута рейда обычно указан дальше
Если указано что-то вроде "13 цет ансейвед героик а нормал потом" - то это LoU Heroic Unsaved и LoU Normal, то есть если тип лута второго и последующих рейдов не указана то это по умолчанию обычный рейд
`;

export default async function AI(request, shop = '') {
	console.log(`[AI] currentDate: ${currentDate}, currentWeekday: ${currentWeekday}, ближайшие дни недели: ${weekdaysInfo}, shop: ${shop}`);

	// Выбираем системную инструкцию в зависимости от магазина
	const systemInstruction = shop === 'Skycoach' ? sysInSky : 
                          shop === 'EpicCarry' ? sysEpic :  `в запросах будут заказы на рейды в world of warcraft, в твоих ответах должен быть только json объект заказа из запроса с следующими параметрами
1 - date(без времени)  если дата не указана в сообщении, то используй текущую дату ${currentDate}, если года нет, то ставим 2025, в формате dd.MM.YYYY. если в заказе указан день недели(а не число), 
то ставь дату на ближайший такой день недели,  ближайшие дни недели: ${weekdaysInfo}
При наличии нескольких JSON объектов дата у них может быть разной:
-если дата не указана в сообщении, то также везде используется ${currentDate}
-если для одного рейда указано только время, а для другого конкретная дата и время, то для первого используем ${currentDate} а для второго уже указанную дату
-если указанная дата подразумевает все рейды в это число, то указываем ее для всех объектов
примеры:
"#1012_596847 | EU Alliance
[World of Warcraft]
Liberation of Undermine Heroic + FREE Normal
 / Group loot / Group loot / 03/06/2025 15:00 CEST / ETA: 1 day
Self-Play" - оба рейда на 03.06.2025
"12345 - Undermine Heroic Raid + FREE Normal Run

https://worldofwarcraft.blizzard.com/en-gb/character/eu/eredar/test

22:00 CEST Heroic
20.03 11:45 CEST Normal" - первый рейд на ${currentDate} а второй на 20.03.2025
"229554 €10,00
eu Liberation Heroic 2+1 Bundle 
Group Loot HC 2+1
Selfplay
  selfplay
Realm - Blackhand
Character Name - Lbladepriest
VPN Country - Germany
Faction - Horde
Battle-tag - Lblade#2897
ETA: 1,5 hour/raid" - все рейды на ${currentDate}
2 - orderid - номер заказа (иногда может быть двойным, тройным и.т.д складывающийся через +, тогда берем все вместе с плюсом, напрмеир 2390_329 + 2390_330), (Run ID - это не номер заказа, 
в таких сообщениях обычно номер заказа что-то вроде S25ABC25C7)
3 - price - цена заказа, только строка без указания валюты, в качестве разделителя используй запятую(например 4.5 USD = 4,5), если json объектов больше 1 то цену вставляем только в первый объект
4 - nickname - ссылка на персонажа из игры или никнейм-сервер.
Определение типа идентификатора:
Ключевое правило: BattleTag (строка формата "Имя#число" или "Имя #число", включая пробелы перед #) НИКОГДА не извлекается и не используется в поле nickname. Если в тексте присутствует символ # рядом с именем (например, "Имя #чи3ло" или "Имя#чи3ло"), это имя не является никнеймом для извлечения
Никнейм: Имя персонажа (например, MyChar), возможно с указанием сервера (MyChar-ServerName).
Ссылка на персонажа: URL-адрес профиля персонажа (например, https://worldofwarcraft.blizzard.com/...).
Приоритет 1 (Ссылка): Если в сообщении есть прямая ссылка на персонажа (например, https://worldofwarcraft.blizzard.com/en-us/character/eu/gordunni/имяперсонажа), используй только ссылку.
Приоритет 2 (Никнейм-Сервер): Если указано имя персонажа и сервер (например, ИмяПерсонажа-Сервер), используй этот формат.
Приоритет 3 (Только Никнейм): Если указано только имя персонажа (и это не BattleTag), используй это имя.
Обработка конструкции "Никнейм (BattleTag)": Если в тексте есть конструкция вида ИмяПерсонажа (BattleTag#1234) (например, Anton (Pepega#3221)), в поле nickname вставляется только ИмяПерсонажа (т.е. Anton). BattleTag в скобках игнорируется.
Обработка изолированного BattleTag: Если в сообщении из идентификаторов присутствует только BattleTag (например, Kelipus#3562 или (PlayerTag#5432)) и нет другого явного никнейма или ссылки, поле nickname остается ПУСТЫМ. Извлечение части имени из BattleTag не производится.
Исключения: Слова Alliance и Horde НЕ должны быть включены в nickname.
Примеры:
Anton (Pepega#3221) -> Anton
Misterius (Chemik#4521) -> Misterius
John-Gordunni -> John-Gordunni
John gordunni -> John-Gordunni
Keleso, Alliance -> Keleso
Perdio Horde -> Perdio
https://worldofwarcraft.blizzard.com/en-us/character/eu/gordunni/MyChar -> https://worldofwarcraft.blizzard.com/en-us/character/eu/gordunni/MyChar
Kelipus#2871 (и нет другого никнейма/ссылки рядом) -> "" (пустое поле)
Kelipus #2871 (и нет другого никнейма/ссылки рядом) -> "" (пустое поле)
(Mulundo#4313) (и нет другого никнейма/ссылки рядом) -> "" (пустое поле)
Riko #2386, Limik -> Limik во все рейды
CoolNick -> CoolNick
CoolNick, Anton#3454 -> CoolNick во все рейды
Распределение никнеймов при нескольких рейдах:
Если в сообщении указан только один никнейм/ссылка, то этот никнейм используется для всех JSON-объектов заказа.
Если в сообщении указано несколько никнеймов/ссылок без явных пояснений, то:
Первый никнейм/ссылка присваивается первому рейду (по умолчанию Heroic, если есть).
Второй никнейм/ссылка присваивается второму рейду (по умолчанию Normal, если есть).
И так далее, распределяя никнеймы по порядку между всеми рейдами.
Если количество никнеймов меньше, чем количество рейдов, и нет явных указаний на распределение, то никнеймы циклически повторяются.
Если никнеймы явно привязаны к конкретным рейдам или персонажам (например, "1st character Maverik ... 2nd character Jennífer"), то распределяй их согласно этим указаниям. 
В случае, когда несколько рейдов (например, 2x Heroic + FREE Normal) и несколько персонажей, и нет явных указаний на то, какой персонаж идет на какой рейд, предполагай, 
что каждый персонаж участвует во всех типах рейдов, указанных для заказа. То есть, если есть 2 Heroic и 2 Normal, и 2 персонажа, то каждый персонаж должен быть назначен на 1 Heroic и 1 Normal рейд.
Если видишь символ # ТО РЯДОМ С НИМ ЭТО НЕ НИКНЕЙМ И ЕГО ИСПОЛЬЗОВАТЬСЯ НЕЛЬЗЯ(даже если # и текст отделяет пробел)
5 - time (в формате HH:MM),  возможно время иногда будет не совпадать формату, тогда ты должен подстроить его, например в сообщении будет "на 18" - изменить это на "18:00" или 20 10 - изменить это на "20:10", ETA: НЕ СЧИТАЕТСЯ ВРЕМЕНЕМ 
И ЕГО СЮДА НЕ ИСПОЛЬЗОВАТЬ(оставлять время пустым если найдено только ETA:) 
Если у нас несколько json объектов то по умолчанию вставляем time только в первый, если явно не указано другое
6 -task, возможные задания: 
LoU Normal
LoU Heroic
Gallywix Normal
Gallywix Heroic
LoU Heroic Unsaved
LoU Normal Unsaved
Gallywix Mythic
LoU Single Boss NM
LoU Single Boss HC
LoU Single Boss Mythic
LoU Heroic ATP ** ppl
LoU Heroic FP ** ppl
LoU Normal ATP ** ppl
LoU Normal FP ** ppl
LoU Mythic x/8 FP 20ppl    - x это число от 1 до 8  
LoU Mythic x/8 ATP 20ppl   - x это число от 1 до 8  
Key
TW Illidan
TW Black Temple
TW Yogg-Saron
TW Ulduar
TW Firelands
где ** это число от 10 до 30   
пытайся подстроить под задание из этого списка, могут бысть сокращения в стиле NM HC для нормала и героика или надписи на русском и.т.п. 

сначала определи сколько боссов нам нужно убить(также иногда в тексте может быть */8 где * - кол-во боссов которое нужно убить из 8, начиная с первого), 
если 8 боссов, то это будет либо LoU Normal либо LoU Heroic, 
если в запросе явно указан только Gallywix то task должен быть Gallywix Normal, Gallywix Heroic, или Gallywix Mythic соответственно, в случаях, когда нужно убить от 2 до 7 боссов это будет LoU Single Boss NM или LoU Single Boss HC  
"LoU Heroic Unsaved" и "LoU Normal Unsaved" используются только в том случае, если в сообщении явно указано "Unsaved" или "Unsaved group loot", или если количество Unsaved Raiders превышает 10. Если указано "Saved" или "Saved group loot", то это обычный рейд (LoU Heroic или LoU Normal соответственно"
Ahead of the Curve и aotc - эти фразы подразумевают Gallywix Heroic
7 -bosses заполняем только в случае когда task -  LoU Single Boss NM или LoU Single Boss HC во всех других случаях тут пусто, заполняем  через \n после каждого босса, сортируем по возрастанию номера из списка(номер оставляем),  список возможных боссов:
 1) Vexie and the Geargrinders
 2) Cauldron of Carnage
 3) Rik Reverb
 4) Stix Bunkjunker
 5) Sprocketmonger Lockenstock
 6) The One-Armed Bandit
 7) Mug'Zee, Heads of Security
 8) Chrome King Gallywix
  названия не обязательно должны быть 1в1 такими же, ищи похожие, если в тексте запроса присутствует фраза 'Gallywix Slain' или 'Gallywix Heroic' или 'Gallywix Normal', то босс 'Chrome King Gallywix' должен быть обязательно добавлен в список bosses, даже если в запросе явно указаны другие боссы. Дополнительные боссы, указанные в запросе (например, через '/' после 'Gallywix Slain'), также добавляются в список bosses, task также остается Single Boss	
8 - pilotinfo - если в сообщении есть информация о том, что заказ piloted или где-то в сообщении видны данные от аккаунта, то вставляем всю эту информацию сюда, логин/пароль(может быть в виде ссылки)/название или номер аккаунта/страна для впн,напдись "remote"  (если у нас несколько json объектов то pilotinfo вставляем во все)
9 - bossComment - если есть информация о том что клиент будет менять персонажа в течении одного рейда, тут заполняем текстом в следующем виде "после x боссов перезайдет на "имя персонажа""
    примеры: в заказе есть строка "Character Name - vasya-server for first 2 then petya-server for last 6"  - пишем "после 2 боссов перезайдет на petya-server"
один рейд - один json объект и если будет заказ в виде х8 Heroic то нужно создать 8 объектов и.т.п.
ЕСЛИ  в тексте есть что-то похожее на Heroic plus Normal, то нужно выдать 2 json  объекта, где у них 
будет совпадать:  orderid и по умолчанию date, если явно не указана другая дата для Normal рейда
task - в первый вставляем LoU Heroic, во второй LoU Normal.
time - по умолчанию вставляем в первый, во втором оставляем пустой, если в тексте явно не указано время для Normal рейда
price - вставляем в первый, во втором оставляем пустым(во всех случаях когда json объектов больше 1)
nickname - если в сообщении находим только один, то ставим одинаковый для обоих, если два без пояснений, то первый ник/ссылку вставляем в Heroic, второй ник/ссылку вставляем в в Normal, если с пояснениями, то определяем никнеймы согласно указаниям
ЕСЛИ есть что-то похожее на 2+1 Bundle значит это 3 рейда указанной перед эти сложности, , например: Heroic 2+1 Bundle - 3 героик рейда и 3 json объекта и больше ничего
ЕСЛИ есть что-то похожее на "Nx [Название рейда] [Сложность] + FREE [Сложность]", где N - число, то это означает N рейдов первой сложности и N рейдов второй (FREE) сложности. Например, "2x Liberation of Undermine Heroic + FREE Normal" означает 2 героических рейда и 2 нормальных рейда
ЕСЛИ есть что-то похожее на "Nx [Название рейда] [Сложность] 2+1 Bundle", где N - число, то это означает N*3 рейдов соответствующей сложности, например x2 Liberation of Undermine  Heroic 2+1 Bundle - это 2*3=6 героических рейдов
Важно: Это общее количество рейдов, которое нужно выполнить, а не количество рейдов на каждого персонажа, если не указано иное.
Логика определения LoU Normal, LoU Heroic, LoU Heroic Unsaved, LoU Normal Unsaved, LoU Heroic ATP ** ppl, LoU Heroic FP ** ppl, LoU Normal ATP ** ppl, LoU Normal FP ** ppl - 
это тип лута в рейде, старайся определить его для каждого по умолчанию либо если указано Group loot то просто Нормал и
Героик; если указано Unsaved group loot то Unsaved; если указано количество Unsaved Raiders, то Unsaved Raiders: 7-10 и меньше это обычный рейд, все что больше считается Unsaved;   если указано Armor and Token priority то ATP; если указано Full loot Priority то FP;
У заказов из нескольки рейдов могут быть разные типы лута у каждого рейда  и если типы перечислены через  /  то используй по порядку упоминания (например Liberation of Undermine Heroic + FREE Normal
 / Group loot / Full priority /  - первый рейд будет обычный героик, а второй фулл приорити нормал), но учитывай что это только типы уже имеющихся рейдов а не дополнительные рейды (Full run не является типом) 
Key в task ставится если есть что-то похожее на M+ runs, Mythic + run и если task=key то time у этого объекта всегда пустой, также неважно сколько ключей указано в запросе, объект с таском Key МОЖЕТ БЫТЬ ТОЛЬКО ОДИН(например Liberation of Undermine Heroic + FREE Normal
 / Group loot / Group loot / 3+1 Mythic - это 3 объекта LoU Heroic, LoU Normal и key)
Если у какого-то таска непонятна сложность то считаем его Heroic по умолчанию
"Liberation of Undermine and M+ runs" - это пакет из героик рейда и ключа, тип лута рейда обычно указан дальше
Если указано что-то вроде "13 цет ансейвед героик а нормал потом" - то это LoU Heroic Unsaved и LoU Normal, то есть если тип лута второго и последующих рейдов не указана то это по умолчанию обычный рейд
`;

	const prompt = `


	${request}`
	let response;
	try {
		response = await limiter.schedule(() =>
			ai.models.generateContent({
				model: 'gemini-2.5-flash-preview-05-20',
				contents: prompt,
				config: {
					thinkingConfig: {
						thinkingBudget: 0,
					},
					systemInstruction: systemInstruction,
					maxOutputTokens: 8000,
					temperature: 0,
					top_p: 1,
					top_k: 1,
				},
			})
		)
	} catch (e) {
		throw {
			error: 'AI request error',
			details: e,
			responseText: response?.text,
			response
		}
	}

	// Сохраняем сырой ответ от ИИ
	const rawAiResponse = response?.text || '';

	let json
	try {
		json = JSON.parse(response.text.replace('```json', '').replace('```', '').replace(/\n/, ''))
	} catch (e) {
		throw {
			error: 'AI parse error',
			details: e,
			responseText: response?.text,
			response
		}
	}
	if (!json) {
		throw {
			error: 'AI empty result',
			responseText: response?.text,
			response
		}
	}

	// Возвращаем объект с обработанными данными и сырым ответом
	return {
		data: json,
		rawResponse: rawAiResponse
	}
} 